import { Q } from '@nozbe/watermelondb';
import database from '../database';
import History from '../models/History';
import Company from '../models/Company';
import { CompanyData } from './companyRepository';

export interface HistoryData {
  id?: string;
  company_id: string;
  viewed_at?: Date;
  company_name?: string;
  number?: string;
  number_id?: number;
}

export interface HistoryWithCompanyDetails extends HistoryData {
  company?: CompanyData | null;
}

class WatermelonHistoryRepository {
  private collection = database.get<History>('history');
  private companyCollection = database.get<Company>('companies');

  async getAll(): Promise<HistoryData[]> {
    try {
      const historyItems = await this.collection.query().fetch();
      return historyItems.map(item => ({
        id: item.id,
        company_id: item.companyId,
        viewed_at: item.viewedAt,
        company_name: item.companyName,
        number: item.number,
        number_id: item.numberId,
      }));
    } catch (error) {
      console.error('Error getting history:', error);
      throw error;
    }
  }

  async getHistoryWithCompanyDetails(): Promise<HistoryWithCompanyDetails[]> {
    try {
      const historyItems = await this.collection
        .query(Q.sortBy('viewed_at', Q.desc))
        .fetch();

      const historyWithDetails: HistoryWithCompanyDetails[] = [];

      for (const item of historyItems) {
        let company: CompanyData | null = null;

        try {
          // Convert string company_id to number for querying
          const companyId = parseInt(item.companyId, 10);

          // Query by company_id field instead of using find() with WatermelonDB ID
          const companyRecords = await this.companyCollection
            .query(Q.where('company_id', companyId))
            .fetch();

          if (companyRecords.length > 0) {
            const companyRecord = companyRecords[0];
            company = {
              id: companyRecord.id,
              company_id: companyRecord.companyId,
              company_name: companyRecord.companyName,
              parent_company: companyRecord.parentCompany,
              company_email: companyRecord.companyEmail,
              company_logo_url: companyRecord.companyLogoUrl,
              company_country: companyRecord.companyCountry,
              company_address: companyRecord.companyAddress,
              company_website: companyRecord.companyWebsite,
              number: companyRecord.number,
              is_whatsapp: companyRecord.isWhatsapp,
              upvote_count: companyRecord.upvoteCount,
              downvote_count: companyRecord.downvoteCount,
              created_at: companyRecord.createdAt,
              updated_at: companyRecord.updatedAt,
            };
          }
        } catch (error) {
          console.log(`Company with ID ${item.companyId} not found in local database:`, error);
          // Company not found, but we still include the history item
        }

        historyWithDetails.push({
          id: item.id,
          company_id: item.companyId,
          viewed_at: item.viewedAt,
          company_name: item.companyName,
          number: item.number,
          number_id: item.numberId,
          company,
        });
      }

      return historyWithDetails;
    } catch (error) {
      console.error('Error getting history with company details:', error);
      throw error;
    }
  }

  async addToHistory(companyId: string): Promise<string> {
    try {
      // Check if this company is already in history
      const existingHistory = await this.collection
        .query(Q.where('company_id', companyId))
        .fetch();

      if (existingHistory.length > 0) {
        // Update the viewed_at timestamp for existing entry
        const historyItem = existingHistory[0];
        await database.write(async () => {
          await historyItem.update(item => {
            item.viewedAt = new Date();
          });
        });
        return historyItem.id;
      } else {
        // Create new history entry
        const newHistoryItem = await database.write(async () => {
          return await this.collection.create(item => {
            item.companyId = companyId;
            item.viewedAt = new Date();
          });
        });
        return newHistoryItem.id;
      }
    } catch (error) {
      console.error('Error adding to history:', error);
      throw error;
    }
  }

  async addNumberTapToHistory(companyName: string, number: string, companyId: number, numberId: number): Promise<string> {
    try {
      // Check if this numberId already exists for this company
    const existingHistory = await this.collection
      .query(
        Q.where('number_id', numberId)
      )
      .fetch();

    if (existingHistory.length > 0) {
      // Update the viewedAt timestamp for existing entry
      const historyItem = existingHistory[0];
      await database.write(async () => {
        await historyItem.update(item => {
          item.viewedAt = new Date();
        });
      });
      console.log(`Number tap already exists in history: ${companyName} - ${number}`);
      return null; // Or return historyItem.id if you want to return the existing id
    }

      // Create new history entry for number tap
      const newHistoryItem = await database.write(async () => {
        return await this.collection.create(item => {
          item.companyId = companyId.toString(); // Store companyId for reference
          item.companyName = companyName;
          item.number = number;
          item.numberId = numberId;
          item.viewedAt = new Date();
        });
      });
      console.log(`Added number tap to history: ${companyName} - ${number}`);
      return newHistoryItem.id;
    } catch (error) {
      console.error('Error adding number tap to history:', error);
      throw error;
    }
  }

  async getNumberTapHistory(): Promise<HistoryData[]> {
    try {
      const historyItems = await this.collection
        .query(Q.sortBy('viewed_at', Q.desc))
        .fetch();

      return historyItems
        .filter(item => item.companyName && item.number) // Only number tap entries
        .map(item => ({
          id: item.id,
          company_id: item.companyId,
          viewed_at: item.viewedAt,
          company_name: item.companyName,
          number: item.number,
          number_id: item.numberId,
        }));
    } catch (error) {
      console.error('Error getting number tap history:', error);
      throw error;
    }
  }

  async removeFromHistory(historyId: string): Promise<void> {
    try {
      await database.write(async () => {
        const historyItem = await this.collection.find(historyId);
        await historyItem.destroyPermanently();
      });
    } catch (error) {
      console.error('Error removing from history:', error);
      throw error;
    }
  }

  async clearHistory(): Promise<void> {
    try {
      await database.write(async () => {
        const allHistory = await this.collection.query().fetch();
        const batch = allHistory.map(item => item.prepareDestroyPermanently());
        await database.batch(batch);
      });
    } catch (error) {
      console.error('Error clearing history:', error);
      throw error;
    }
  }

  async getRecentHistory(limit: number = 10): Promise<HistoryWithCompanyDetails[]> {
    try {
      const historyItems = await this.collection
        .query(Q.sortBy('viewed_at', Q.desc), Q.take(limit))
        .fetch();

      const historyWithDetails: HistoryWithCompanyDetails[] = [];

      for (const item of historyItems) {
        let company: CompanyData | null = null;

        try {
          const companyRecord = await this.companyCollection.find(item.companyId);
          company = {
            id: companyRecord.id,
            company_id: companyRecord.companyId,
            company_name: companyRecord.companyName,
            parent_company: companyRecord.parentCompany,
            company_email: companyRecord.companyEmail,
            company_logo_url: companyRecord.companyLogoUrl,
            company_country: companyRecord.companyCountry,
            company_address: companyRecord.companyAddress,
            company_website: companyRecord.companyWebsite,
            number: companyRecord.number,
            is_whatsapp: companyRecord.isWhatsapp,
            upvote_count: companyRecord.upvoteCount,
            downvote_count: companyRecord.downvoteCount,
            created_at: companyRecord.createdAt,
            updated_at: companyRecord.updatedAt,
          };
        } catch (error) {
          console.log(`Company with ID ${item.companyId} not found in local database`);
        }

        historyWithDetails.push({
          id: item.id,
          company_id: item.companyId,
          viewed_at: item.viewedAt,
          company_name: item.companyName,
          number: item.number,
          number_id: item.numberId,
          company,
        });
      }

      return historyWithDetails;
    } catch (error) {
      console.error('Error getting recent history:', error);
      throw error;
    }
  }
}

export default new WatermelonHistoryRepository();
