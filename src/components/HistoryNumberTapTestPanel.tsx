import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Alert} from 'react-native';
import HistoryNumberTapTester from '../utils/testHistoryNumberTap';
import historyRepository from '../database/watermelon/repositories/historyRepository';

const HistoryNumberTapTestPanel = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<string>('');

  const runTest = async () => {
    setIsLoading(true);
    setTestResults('Running tests...');
    
    try {
      await HistoryNumberTapTester.runAllTests();
      setTestResults('✅ All tests passed! Check console for details.');
    } catch (error) {
      setTestResults(`❌ Tests failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSingleNumberTap = async () => {
    setIsLoading(true);
    try {
      const testCompanyName = 'Sample Company';
      const testNumber = '+91-9876543210';
      
      await historyRepository.addNumberTapToHistory(testCompanyName, testNumber);
      Alert.alert('Success', `Added number tap: ${testCompanyName} - ${testNumber}`);
      setTestResults(`✅ Added: ${testCompanyName} - ${testNumber}`);
    } catch (error) {
      Alert.alert('Error', `Failed to add number tap: ${error}`);
      setTestResults(`❌ Failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const viewHistory = async () => {
    setIsLoading(true);
    try {
      const history = await historyRepository.getHistoryWithCompanyDetails();
      const numberTapEntries = history.filter(item => item.company_name && item.number);
      
      Alert.alert(
        'History Summary',
        `Total entries: ${history.length}\nNumber tap entries: ${numberTapEntries.length}`,
      );
      
      setTestResults(`📊 Total: ${history.length}, Number taps: ${numberTapEntries.length}`);
    } catch (error) {
      Alert.alert('Error', `Failed to get history: ${error}`);
      setTestResults(`❌ Failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>History Number Tap Test Panel</Text>
      
      <TouchableOpacity
        style={[styles.button, isLoading && styles.buttonDisabled]}
        onPress={runTest}
        disabled={isLoading}>
        <Text style={styles.buttonText}>Run All Tests</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, isLoading && styles.buttonDisabled]}
        onPress={testSingleNumberTap}
        disabled={isLoading}>
        <Text style={styles.buttonText}>Test Single Number Tap</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, isLoading && styles.buttonDisabled]}
        onPress={viewHistory}
        disabled={isLoading}>
        <Text style={styles.buttonText}>View History Summary</Text>
      </TouchableOpacity>

      {testResults ? (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsText}>{testResults}</Text>
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    margin: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
    color: '#333',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    marginTop: 15,
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  resultsText: {
    fontSize: 14,
    color: '#333',
  },
});

export default HistoryNumberTapTestPanel;
