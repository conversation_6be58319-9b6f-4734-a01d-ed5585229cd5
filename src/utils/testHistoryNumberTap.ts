/**
 * Test utility for verifying number tap history functionality
 */

import historyRepository from '../database/watermelon/repositories/historyRepository';

export class HistoryNumberTapTester {
  /**
   * Test adding a number tap to history
   */
  static async testAddNumberTap(): Promise<void> {
    try {
      console.log('🧪 Testing number tap history functionality...');
      
      // Test data
      const testCompanyName = 'Test Company';
      const testNumber = '+91-1234567890';
      
      // Add number tap to history
      console.log(`Adding number tap: ${testCompanyName} - ${testNumber}`);
      const historyId = await historyRepository.addNumberTapToHistory(testCompanyName, testNumber);
      console.log(`✅ Number tap added to history with ID: ${historyId}`);
      
      // Verify it was added
      const allHistory = await historyRepository.getAll();
      const numberTapEntries = allHistory.filter(item => item.company_name && item.number);
      console.log(`📊 Total number tap entries in history: ${numberTapEntries.length}`);
      
      // Show the latest entry
      if (numberTapEntries.length > 0) {
        const latestEntry = numberTapEntries[numberTapEntries.length - 1];
        console.log('📱 Latest number tap entry:', {
          company_name: latestEntry.company_name,
          number: latestEntry.number,
          viewed_at: latestEntry.viewed_at,
        });
      }
      
      console.log('✅ Number tap history test completed successfully!');
    } catch (error) {
      console.error('❌ Number tap history test failed:', error);
      throw error;
    }
  }
  
  /**
   * Test retrieving history with company details
   */
  static async testGetHistoryWithDetails(): Promise<void> {
    try {
      console.log('🧪 Testing history retrieval with details...');
      
      const historyWithDetails = await historyRepository.getHistoryWithCompanyDetails();
      console.log(`📊 Total history entries: ${historyWithDetails.length}`);
      
      // Separate number tap entries from company view entries
      const numberTapEntries = historyWithDetails.filter(item => item.company_name && item.number);
      const companyViewEntries = historyWithDetails.filter(item => !item.company_name && !item.number);
      
      console.log(`📱 Number tap entries: ${numberTapEntries.length}`);
      console.log(`🏢 Company view entries: ${companyViewEntries.length}`);
      
      // Show sample entries
      if (numberTapEntries.length > 0) {
        console.log('📱 Sample number tap entry:', {
          company_name: numberTapEntries[0].company_name,
          number: numberTapEntries[0].number,
          viewed_at: numberTapEntries[0].viewed_at,
        });
      }
      
      if (companyViewEntries.length > 0) {
        console.log('🏢 Sample company view entry:', {
          company_id: companyViewEntries[0].company_id,
          company_name: companyViewEntries[0].company?.company_name,
          viewed_at: companyViewEntries[0].viewed_at,
        });
      }
      
      console.log('✅ History retrieval test completed successfully!');
    } catch (error) {
      console.error('❌ History retrieval test failed:', error);
      throw error;
    }
  }
  
  /**
   * Run all history tests
   */
  static async runAllTests(): Promise<void> {
    try {
      console.log('🚀 Starting all history number tap tests...\n');
      
      await this.testAddNumberTap();
      console.log('');
      await this.testGetHistoryWithDetails();
      
      console.log('\n🎉 All history number tap tests completed successfully!');
    } catch (error) {
      console.error('\n💥 History number tap tests failed:', error);
      throw error;
    }
  }
}

export default HistoryNumberTapTester;
